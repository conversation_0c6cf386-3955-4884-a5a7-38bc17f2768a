using Subhlagan.Application.Logging;
using Subhlagan.Application.ScheduleTasks;
using Subhlagan.Application.Messages;
using Subhlagan.Application.Profiles;
using Subhlagan.Application.Users;
using Subhlagan.Application.Customers;
using Subhlagan.Core.Domain.Messages;
using Subhlagan.Core.Domain.Enum;
using Newtonsoft.Json;
using Subhlagan.Core.Domain;
using Subhlagan.Application.QueuedEmails;
using Subhlagan.Application.Localization;
using Subhlagan.Core;
using Subhlagan.Core.Configuration;
using Subhlagan.Application.Media;
using Subhlagan.Core.Domain.Customers;

namespace Subhlagan.Application.crm.Tasks
{
    /// <summary>
    /// Represents a task for processing and sending mass email campaigns
    /// </summary>
    public class MassEmailCampaignSendTask : IScheduleTask
    {
        #region Fields

        private readonly IMassEmailService _massEmailService;
        private readonly IProfileService _profileService;
        private readonly ProfileMessageService _profileMessageService;
        private readonly IUserService _userService;
        private readonly ICustomerService _customerService;
        private readonly ILanguageService _languageService;
        private readonly ILogger _logger;
        private readonly IEmailAccountService _emailAccountService;
        private readonly IWhatsAppMessageService _whatsAppMessageService;
        private readonly IDownloadService _downloadService;
        private readonly AppSettings _appSettings;

        #endregion

        #region Ctor

        public MassEmailCampaignSendTask(
            IMassEmailService massEmailService,
            IProfileService profileService,
            ProfileMessageService profileMessageService,
            IUserService userService,
            ICustomerService customerService,
            ILanguageService languageService,
            ILogger logger,
            IEmailAccountService emailAccountService,
            IWhatsAppMessageService whatsAppMessageService,
            IDownloadService downloadService,
            AppSettings appSettings)
        {
            _massEmailService = massEmailService;
            _profileService = profileService;
            _profileMessageService = profileMessageService;
            _userService = userService;
            _customerService = customerService;
            _languageService = languageService;
            _logger = logger;
            _emailAccountService = emailAccountService;
            _whatsAppMessageService = whatsAppMessageService;
            _downloadService = downloadService;
            _appSettings = appSettings;
        }

        #endregion

        #region Methods

        /// <summary>
        /// Executes the mass email campaign send task
        /// </summary>
        public async Task ExecuteAsync()
        {
            var currentTimeUtc = DateTime.UtcNow;

            try
            {
                await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - Starting mass email campaign send task at {currentTimeUtc}");

                // Get scheduled campaigns that are due for sending
                var scheduledCampaigns = await _massEmailService.GetAllMassEmailCampaignsAsync(
                    status: MassEmailCampaignStatus.Scheduled,
                    pageSize: 50 // Process up to 50 campaigns at once
                );

                if (!scheduledCampaigns.Any())
                {
                    await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - No scheduled campaigns found for processing");
                    return;
                }

                await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - Found {scheduledCampaigns.Count} scheduled campaigns for processing");

                int campaignsProcessed = 0;
                int totalEmailsSent = 0;
                int totalEmailsFailed = 0;

                foreach (var campaign in scheduledCampaigns)
                {
                    try
                    {
                        // Check if campaign is due for sending (with 60-minute tolerance)
                        if (campaign.ScheduledDateUtc.HasValue)
                        {
                            const int toleranceMinutes = 60;
                            var toleranceTime = campaign.ScheduledDateUtc.Value.AddMinutes(-toleranceMinutes);
                            
                            if (toleranceTime > currentTimeUtc)
                            {
                                var minutesUntilDue = (int)(toleranceTime - currentTimeUtc).TotalMinutes;
                                await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - Campaign '{campaign.Name}' (ID: {campaign.Id}) not yet due for sending. Scheduled: {campaign.ScheduledDateUtc}, Current: {currentTimeUtc}, Minutes until tolerance window: {minutesUntilDue}");
                                continue;
                            }
                            else if (campaign.ScheduledDateUtc.Value < currentTimeUtc)
                            {
                                var minutesLate = (int)(currentTimeUtc - campaign.ScheduledDateUtc.Value).TotalMinutes;
                                await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - Campaign '{campaign.Name}' (ID: {campaign.Id}) is {minutesLate} minutes late but within tolerance window ({toleranceMinutes} minutes). Processing now.");
                            }
                        }

                        // Parse attachments for logging
                        var attachmentDownloadIds = ParseAttachmentDownloadIds(campaign.AttachmentDownloadIds);
                        var attachmentInfo = attachmentDownloadIds.Any() ? $" with {attachmentDownloadIds.Count} attachments" : "";
                        
                        await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - Processing campaign '{campaign.Name}' (ID: {campaign.Id}){attachmentInfo}");

                        var result = await ProcessCampaignAsync(campaign);

                        campaignsProcessed++;
                        totalEmailsSent += result.sentCount;
                        totalEmailsFailed += result.failedCount;

                        await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - Campaign '{campaign.Name}' processing completed. Sent: {result.sentCount}, Failed: {result.failedCount}");
                    }
                    catch (Exception ex)
                    {
                        await _logger.ErrorAsync($"{nameof(MassEmailCampaignSendTask)} - Failed to process campaign '{campaign.Name}' (ID: {campaign.Id})", ex);

                        // Mark campaign as failed
                        try
                        {
                            campaign.Status = MassEmailCampaignStatus.Failed;
                            campaign.CompletedOnUtc = DateTime.UtcNow;
                            await _massEmailService.UpdateMassEmailCampaignAsync(campaign);
                        }
                        catch (Exception updateEx)
                        {
                            await _logger.ErrorAsync($"{nameof(MassEmailCampaignSendTask)} - Failed to update campaign status to failed for campaign {campaign.Id}", updateEx);
                        }
                    }
                }

                await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - Mass email campaign send task completed. " +
                    $"Campaigns processed: {campaignsProcessed}, Total emails sent: {totalEmailsSent}, Total emails failed: {totalEmailsFailed}");
            }
            catch (Exception ex)
            {
                await _logger.ErrorAsync($"{nameof(MassEmailCampaignSendTask)} - Error in mass email campaign send task", ex);
            }
        }

        /// <summary>
        /// Processes a single mass email campaign
        /// </summary>
        /// <param name="campaign">The campaign to process</param>
        /// <returns>A tuple containing sent count and failed count</returns>
        private async Task<(int sentCount, int failedCount)> ProcessCampaignAsync(MassEmailCampaign campaign)
        {
            // Check if testing mode is enabled
            var commonConfig = _appSettings.Get<CommonConfig>();
            var isTestingMode = commonConfig.MassEmailTestingEnabled;
            var testEmails = commonConfig.MassEmailTestEmails;

            if (isTestingMode)
            {
                await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - TESTING MODE ENABLED for campaign '{campaign.Name}' (ID: {campaign.Id}). Emails will be sent to test addresses: {string.Join(", ", testEmails)}");
            }

            // Update campaign status to InProgress
            campaign.Status = MassEmailCampaignStatus.InProgress;
            campaign.StartedOnUtc = DateTime.UtcNow;
            await _massEmailService.UpdateMassEmailCampaignAsync(campaign);

            // Log campaign start
            await _massEmailService.InsertMassEmailLogAsync(new MassEmailLog
            {
                MassEmailCampaignId = campaign.Id,
                LogLevel = MassEmailLogLevel.Info,
                Message = $"Starting campaign processing{(isTestingMode ? " (TESTING MODE)" : "")}",
                CreatedOnUtc = DateTime.UtcNow
            });

            int sentCount = 0;
            int failedCount = 0;
            string campaignMediaId = null; // Store media ID for campaign-wide use

            try
            {
                // Upload campaign image to WhatsApp once if present (optimization for mass campaigns)
                if (campaign.ImageDownloadId.HasValue && campaign.ImageDownloadId.Value > 0 && !string.IsNullOrEmpty(campaign.WhatsAppTemplateName))
                {
                    try
                    {
                        var imageDownload = await _downloadService.GetDownloadByIdAsync(campaign.ImageDownloadId.Value);
                        if (imageDownload != null && imageDownload.DownloadBinary != null && imageDownload.DownloadBinary.Length > 0)
                        {
                            await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - Uploading campaign image '{imageDownload.Filename}' to WhatsApp for campaign '{campaign.Name}' (upload once, use for all recipients)");
                            
                            campaignMediaId = await _whatsAppMessageService.UploadMediaAsync(
                                imageDownload.DownloadBinary,
                                imageDownload.Filename,
                                imageDownload.ContentType
                            );

                            if (!string.IsNullOrEmpty(campaignMediaId))
                            {
                                await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - Campaign image uploaded successfully. Media ID: {campaignMediaId} for campaign '{campaign.Name}'");
                                
                                // Log the media upload success
                                await _massEmailService.InsertMassEmailLogAsync(new MassEmailLog
                                {
                                    MassEmailCampaignId = campaign.Id,
                                    LogLevel = MassEmailLogLevel.Info,
                                    Message = $"Campaign image '{imageDownload.Filename}' uploaded to WhatsApp. Media ID: {campaignMediaId}",
                                    CreatedOnUtc = DateTime.UtcNow
                                });
                            }
                            else
                            {
                                await _logger.WarningAsync($"{nameof(MassEmailCampaignSendTask)} - Failed to upload campaign image to WhatsApp for campaign '{campaign.Name}'. Will use fallback URL approach for WhatsApp messages.");
                                
                                // Log the upload failure
                                await _massEmailService.InsertMassEmailLogAsync(new MassEmailLog
                                {
                                    MassEmailCampaignId = campaign.Id,
                                    LogLevel = MassEmailLogLevel.Warning,
                                    Message = $"Failed to upload campaign image to WhatsApp. Will use URL fallback approach.",
                                    CreatedOnUtc = DateTime.UtcNow
                                });
                            }
                        }
                        else
                        {
                            await _logger.WarningAsync($"{nameof(MassEmailCampaignSendTask)} - Campaign image not found or empty for campaign '{campaign.Name}' (ImageDownloadId: {campaign.ImageDownloadId.Value})");
                        }
                    }
                    catch (Exception mediaUploadEx)
                    {
                        await _logger.WarningAsync($"{nameof(MassEmailCampaignSendTask)} - Error uploading campaign image to WhatsApp for campaign '{campaign.Name}': {mediaUploadEx.Message}. Will use URL fallback approach.", mediaUploadEx);
                        
                        // Log the upload exception
                        await _massEmailService.InsertMassEmailLogAsync(new MassEmailLog
                        {
                            MassEmailCampaignId = campaign.Id,
                            LogLevel = MassEmailLogLevel.Warning,
                            Message = $"Error uploading campaign image to WhatsApp: {mediaUploadEx.Message}. Will use URL fallback.",
                            CreatedOnUtc = DateTime.UtcNow
                        });
                    }
                }

                // Parse recipient filters
                var filters = ParseRecipientFilters(campaign.RecipientFilters);

                // Get profiles based on filters in batches
                const int batchSize = 100; // Process 100 profiles at a time
                int pageIndex = 0;
                bool hasMoreProfiles = true;

                do
                {
                    var profiles = new List<Profile>();
                    if (isTestingMode)
                    {
                        if (pageIndex == 0) // Only process test emails once
                        {
                            await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - Processing test emails for campaign '{campaign.Name}' (ID: {campaign.Id})");
                            foreach (var email in testEmails)
                            {
                                var customer = await _customerService.GetCustomerByEmailAsync(email);
                                if (customer != null)
                                {
                                    var profile = await _profileService.GetProfileByCustomerIdAsync(customer.Id);
                                    if (profile != null)
                                    {
                                        profiles.Add(profile);
                                    }
                                }
                            }
                        }
                        hasMoreProfiles = false; // Ensure loop breaks after processing test emails
                    }
                    else
                    {
                        profiles = await GetProfilesForCampaignAsync(filters, pageIndex, batchSize);
                        hasMoreProfiles = profiles.Any(); // Continue only if more profiles exist
                    }


                    if (!profiles.Any())
                    {
                        if (pageIndex == 0)
                        {
                            await _logger.WarningAsync($"{nameof(MassEmailCampaignSendTask)} - No profiles found for campaign '{campaign.Name}' (ID: {campaign.Id})");
                        }
                        break;
                    }

                    await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - Processing batch {pageIndex + 1} with {profiles.Count} profiles for campaign '{campaign.Name}'{(isTestingMode ? " (TESTING MODE)" : "")}");

                    // Process each profile in the batch
                    int profileIndex = 0;
                    foreach (var profileTemp in profiles)
                    {
                        try
                        {
                            var profile = await _profileService.GetProfileByCustomerIdAsync(profileTemp.CustomerId);
                            if (profile == null)
                            {
                                await _logger.WarningAsync($"{nameof(MassEmailCampaignSendTask)} - Profile not found for CustomerId {profileTemp.CustomerId} in campaign '{campaign.Name}' (ProfileId: {profileTemp.Id})");
                                continue;
                            }
                            // Get or create campaign recipient record
                            var recipient = await GetOrCreateCampaignRecipientAsync(campaign.Id, profile);

                            // Skip if already processed
                            if (recipient.Status == MassEmailRecipientStatus.Sent || recipient.Status == MassEmailRecipientStatus.Failed)
                            {
                                await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - Skipping already processed recipient {recipient.Email} for campaign {campaign.Id}");
                                continue;
                            }

                            // Update recipient status to processing
                            recipient.Status = MassEmailRecipientStatus.Processing;
                            await _massEmailService.UpdateMassEmailRecipientAsync(recipient);

                            // Send email using ProfileMessageService
                            await SendCampaignEmailAsync(campaign, profile, recipient);

                            // Update recipient status to sent
                            recipient.Status = MassEmailRecipientStatus.Sent;
                            recipient.SentOnUtc = DateTime.UtcNow;
                            await _massEmailService.UpdateMassEmailRecipientAsync(recipient);

                            // Send WhatsApp message if template is specified
                            await SendWhatsAppMessageAsync(campaign, profile, campaignMediaId);

                            sentCount++;
                            profileIndex++;

                        }
                        catch (Exception ex)
                        {
                            await _logger.ErrorAsync($"{nameof(MassEmailCampaignSendTask)} - Failed to send email to profile {profileTemp.Id} for campaign '{campaign.Name}'", ex);

                            // Update recipient status to failed
                            try
                            {
                                var profile = await _profileService.GetProfileByCustomerIdAsync(profileTemp.CustomerId);

                                var recipient = await GetOrCreateCampaignRecipientAsync(campaign.Id, profile);
                                recipient.Status = MassEmailRecipientStatus.Failed;
                                recipient.ErrorMessage = ex.Message;
                                await _massEmailService.UpdateMassEmailRecipientAsync(recipient);
                            }
                            catch (Exception updateEx)
                            {
                                await _logger.ErrorAsync($"{nameof(MassEmailCampaignSendTask)} - Failed to update recipient status for profile {profileTemp.Id}", updateEx);
                            }

                            failedCount++;
                            profileIndex++;
                        }
                    }

                    pageIndex++;

                } while (hasMoreProfiles);

                // Update campaign completion
                campaign.Status = MassEmailCampaignStatus.Completed;
                campaign.CompletedOnUtc = DateTime.UtcNow;
                campaign.SentCount = sentCount;
                campaign.FailedCount = failedCount;
                await _massEmailService.UpdateMassEmailCampaignAsync(campaign);

                // Log campaign completion
                await _massEmailService.InsertMassEmailLogAsync(new MassEmailLog
                {
                    MassEmailCampaignId = campaign.Id,
                    LogLevel = MassEmailLogLevel.Info,
                    Message = $"Campaign completed successfully. Sent: {sentCount}, Failed: {failedCount}",
                    CreatedOnUtc = DateTime.UtcNow
                });

                return (sentCount, failedCount);
            }
            catch (Exception ex)
            {
                // Update campaign status to failed
                campaign.Status = MassEmailCampaignStatus.Failed;
                campaign.CompletedOnUtc = DateTime.UtcNow;
                campaign.SentCount = sentCount;
                campaign.FailedCount = failedCount;
                await _massEmailService.UpdateMassEmailCampaignAsync(campaign);

                // Log campaign failure
                await _massEmailService.InsertMassEmailLogAsync(new MassEmailLog
                {
                    MassEmailCampaignId = campaign.Id,
                    LogLevel = MassEmailLogLevel.Error,
                    Message = $"Campaign failed: {ex.Message}",
                    CreatedOnUtc = DateTime.UtcNow
                });

                throw; // Re-throw to be handled by the caller
            }
        }

        /// <summary>
        /// Sends campaign email to a profile using ProfileMessageService
        /// </summary>
        /// <param name="campaign">The campaign</param>
        /// <param name="profile">The profile</param>
        /// <param name="recipient">The recipient record</param>
        private async Task SendCampaignEmailAsync(MassEmailCampaign campaign, Profile profile, MassEmailRecipient recipient)
        {
            // Get the email account for the campaign
            // var user = await _userService.GetUserByIdAsync(campaign.UserId);
            var emailAccount = await _emailAccountService.GetEmailAccountByIdAsync(campaign.EmailAccountId);
            if (emailAccount == null)
                throw new KsException("Email account can't be loaded");

            // Ensure Customer is loaded
            if (profile.Customer == null)
                throw new KsException($"Profile customer data is not loaded for profile ID {profile.Id}");

            // Get the language for the profile
            var languageId = profile.Customer.LanguageId ?? 1; // Default to primary language
            var language = await _languageService.GetLanguageByIdAsync(languageId);
            if (language == null || !language.Published)
            {
                var defaultLanguage = (await _languageService.GetAllLanguagesAsync()).FirstOrDefault();
                if (defaultLanguage == null)
                    throw new Exception("No active language could be loaded");
                languageId = defaultLanguage.Id;
            }

            // Get customer name with fallback
            var customerName = await _profileService.GetProfileFullNameAsync(profile) ??
                             profile.Customer?.FirstName ??
                             "Dear Customer";

            var body = campaign.Body.Replace("[CustomerName]", customerName);

            // Parse attachments from campaign
            var attachmentDownloadIds = ParseAttachmentDownloadIds(campaign.AttachmentDownloadIds);
            
            // Include image attachment if present - validate before adding to ensure it exists
            if (campaign.ImageDownloadId.HasValue && campaign.ImageDownloadId.Value > 0)
            {
                try
                {
                    var imageDownload = await _downloadService.GetDownloadByIdAsync(campaign.ImageDownloadId.Value);
                    if (imageDownload != null && imageDownload.DownloadBinary != null && imageDownload.DownloadBinary.Length > 0)
                    {
                        attachmentDownloadIds.Add(campaign.ImageDownloadId.Value);
                        await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - Image attachment '{imageDownload.Filename}' added to email for campaign '{campaign.Name}'");
                    }
                    else
                    {
                        await _logger.WarningAsync($"{nameof(MassEmailCampaignSendTask)} - Image attachment not found or empty for campaign '{campaign.Name}' (ImageDownloadId: {campaign.ImageDownloadId.Value})");
                    }
                }
                catch (Exception ex)
                {
                    await _logger.WarningAsync($"{nameof(MassEmailCampaignSendTask)} - Error validating image attachment for campaign '{campaign.Name}': {ex.Message}. Email will be sent without image attachment.");
                }
            }
            
            // Enhanced logging with attachment information
            var attachmentInfo = attachmentDownloadIds.Any() ? $" with {attachmentDownloadIds.Count} attachments" : "";
            await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - Sending email to {profile.Customer?.Email} for campaign '{campaign.Name}'{attachmentInfo}");

            // Send email using ProfileMessageService
            var emailIds = await _profileMessageService.SendProfileMessageAsync(
                profile: profile,
                emailAccount: emailAccount,
                subject: campaign.Subject,
                body: body,
                dontSendBeforeDateUtc: null,
                languageId: languageId,
                attachmentDownloadIds: attachmentDownloadIds
            );

            // Update recipient with queued email ID if available
            if (emailIds.Any())
            {
                recipient.QueuedEmailId = emailIds.First();
                await _massEmailService.UpdateMassEmailRecipientAsync(recipient);
            }
        }

        /// <summary>
        /// Gets or creates a campaign recipient record
        /// </summary>
        /// <param name="campaignId">Campaign ID</param>
        /// <param name="profile">Profile</param>
        /// <param name="isTestingMode">Whether testing mode is enabled</param>
        /// <param name="testEmails">Array of test email addresses</param>
        /// <param name="profileIndex">Index of the profile being processed</param>
        /// <returns>MassEmailRecipient</returns>
        private async Task<MassEmailRecipient> GetOrCreateCampaignRecipientAsync(int campaignId, Profile profile)
        {
            // Try to get existing recipient
            var recipients = await _massEmailService.GetCampaignRecipientsAsync(campaignId, pageSize: 1);
            var existingRecipient = recipients.FirstOrDefault(r => r.CustomerId == profile.CustomerId);

            if (existingRecipient != null)
            {
                return existingRecipient;
            }

            // Ensure Customer is loaded
            if (profile.Customer == null)
                throw new KsException($"Profile customer data is not loaded for profile ID {profile.Id}");

            // Determine email to use
            var emailToUse = profile.Customer.Email;

            // Create new recipient
            var recipient = new MassEmailRecipient
            {
                MassEmailCampaignId = campaignId,
                CustomerId = profile.CustomerId,
                Email = emailToUse,
                Name = $"{profile.Customer?.FirstName} {profile.Customer?.LastName}".Trim(),
                Status = MassEmailRecipientStatus.Pending,
                CreatedOnUtc = DateTime.UtcNow
            };

            await _massEmailService.InsertMassEmailRecipientAsync(recipient);
            return recipient;
        }

        /// <summary>
        /// Gets profiles for campaign based on filters
        /// </summary>
        /// <param name="filters">Recipient filters</param>
        /// <param name="pageIndex">Page index</param>
        /// <param name="pageSize">Page size</param>
        /// <returns>List of profiles</returns>
        private async Task<IList<Profile>> GetProfilesForCampaignAsync(dynamic filters, int pageIndex, int pageSize)
        {
            // Parse filters
            DateTime? registrationDateFrom = null;
            DateTime? registrationDateTo = null;
            string profileStatus = null;
            List<int> profileCategoryIds = null;

            if (filters != null)
            {
                if (filters.RegistrationDateFrom != null)
                {
                    DateTime.TryParse(filters.RegistrationDateFrom.ToString(), out DateTime dateFrom);
                    registrationDateFrom = dateFrom;
                }

                if (filters.RegistrationDateTo != null)
                {
                    DateTime.TryParse(filters.RegistrationDateTo.ToString(), out DateTime dateTo);
                    registrationDateTo = dateTo;
                }

                profileStatus = filters.ProfileStatus?.ToString();

                if (filters.ProfileCategoryIds != null && filters.ProfileCategoryIds is Newtonsoft.Json.Linq.JArray)
                {
                    var categoryArray = filters.ProfileCategoryIds as Newtonsoft.Json.Linq.JArray;
                    profileCategoryIds = categoryArray?.ToObject<List<int>>();
                }
            }

            // Get profiles based on filters
            var profiles = await _profileService.GetAllProfilesAsync(
                createdFromUtc: registrationDateFrom,
                createdToUtc: registrationDateTo,
                pageIndex: pageIndex,
                pageSize: pageSize,
                matchResultIds: new List<int> { (int)MatchResult.Active } // Only active profiles
            );

            return profiles.ToList();
        }

        /// <summary>
        /// Parses recipient filters from JSON string
        /// </summary>
        /// <param name="filtersJson">JSON string</param>
        /// <returns>Dynamic object with filters</returns>
        private dynamic ParseRecipientFilters(string filtersJson)
        {
            if (string.IsNullOrEmpty(filtersJson))
            {
                return null;
            }

            try
            {
                return JsonConvert.DeserializeObject(filtersJson);
            }
            catch (Exception ex)
            {
                _logger.ErrorAsync($"{nameof(MassEmailCampaignSendTask)} - Failed to parse recipient filters: {filtersJson}", ex);
                return null;
            }
        }

        /// <summary>
        /// Parses attachment download IDs from comma-separated string
        /// </summary>
        /// <param name="attachmentIds">Comma-separated attachment IDs</param>
        /// <returns>List of attachment download IDs</returns>
        private List<int> ParseAttachmentDownloadIds(string attachmentIds)
        {
            if (string.IsNullOrEmpty(attachmentIds))
                return new List<int>();
                
            try
            {
                return attachmentIds.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => id.Trim())
                    .Where(id => int.TryParse(id, out _))
                    .Select(int.Parse)
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.WarningAsync($"{nameof(MassEmailCampaignSendTask)} - Failed to parse attachment IDs: {attachmentIds}", ex);
                return new List<int>();
            }
        }

        /// <summary>
        /// Sends WhatsApp message to a profile if template is specified, with image if available
        /// </summary>
        /// <param name="campaign">The campaign</param>
        /// <param name="profile">The profile</param>
        /// <param name="campaignMediaId">Pre-uploaded media ID for campaign image (optimization)</param>
        private async Task SendWhatsAppMessageAsync(MassEmailCampaign campaign, Profile profile, string campaignMediaId = null)
        {
            // Skip if no WhatsApp template specified
            if (string.IsNullOrEmpty(campaign.WhatsAppTemplateName))
                return;

            try
            {
                if (!profile.WhatsAppVerified)
                    return;

                var mobileNumber = _profileService.GetDecryptedMobileNumber(profile);
                if (string.IsNullOrEmpty(mobileNumber))
                    return;

                // Get customer name with fallback
                var customerName = await _profileService.GetProfileFullNameAsync(profile) ?? 
                                 profile.Customer.FirstName ?? 
                                 "Dear Customer";

                // Prepare template parameters
                var parameters = new Dictionary<string, string>
                {
                    { "1", customerName }
                };

                // Check if campaign has image attachment for WhatsApp - use optimized media ID approach if available
                if (campaign.ImageDownloadId.HasValue && campaign.ImageDownloadId.Value > 0)
                {
                    try
                    {
                        if (!string.IsNullOrEmpty(campaignMediaId))
                        {
                            // Use optimized media ID approach (upload once, use many times)
                            await _whatsAppMessageService.SendTemplateMessageWithMediaIdAsync(
                                mobileNumber, 
                                campaign.WhatsAppTemplateName, 
                                campaignMediaId,
                                parameters,
                                campaign.Subject
                            );
                            
                            await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - WhatsApp image + template sequence sent successfully to {mobileNumber.MaskMobileNumber()} using template '{campaign.WhatsAppTemplateName}' and media ID '{campaignMediaId}' for campaign '{campaign.Name}' (optimized: pre-uploaded media)");
                        }
                        else
                        {
                            // Fallback to URL approach if media ID not available
                            var imageDownload = await _downloadService.GetDownloadByIdAsync(campaign.ImageDownloadId.Value);
                            if (imageDownload != null && imageDownload.DownloadBinary != null && imageDownload.DownloadBinary.Length > 0)
                            {
                                // Generate image URL for WhatsApp template header  
                                var imageUrl = $"{CompanyInfo.Url.TrimEnd('/')}/Download/GetImageByDownloadId?downloadId={campaign.ImageDownloadId.Value}";
                                
                                // Send WhatsApp template message with image header (URL fallback)
                                await _whatsAppMessageService.SendTemplateMessageWithImageAsync(
                                    mobileNumber, 
                                    campaign.WhatsAppTemplateName, 
                                    imageUrl,
                                    parameters,
                                    campaign.Subject
                                );
                                
                                await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - WhatsApp image + template sequence sent successfully to {mobileNumber.MaskMobileNumber()} using template '{campaign.WhatsAppTemplateName}' and image '{imageDownload.Filename}' for campaign '{campaign.Name}' (fallback: URL approach)");
                            }
                            else
                            {
                                // Send regular template message without image if image not found
                                await _whatsAppMessageService.SendTemplateMessageAsync(
                                    mobileNumber, 
                                    campaign.WhatsAppTemplateName, 
                                    parameters
                                );
                                
                                await _logger.WarningAsync($"{nameof(MassEmailCampaignSendTask)} - Image attachment not found or empty for campaign '{campaign.Name}' (ImageDownloadId: {campaign.ImageDownloadId.Value}). Sent WhatsApp message without image.");
                            }
                        }
                    }
                    catch (Exception imageEx)
                    {
                        // Fallback to regular template message if image processing fails
                        await _whatsAppMessageService.SendTemplateMessageAsync(
                            mobileNumber, 
                            campaign.WhatsAppTemplateName, 
                            parameters
                        );
                        
                        await _logger.WarningAsync($"{nameof(MassEmailCampaignSendTask)} - Error processing image for WhatsApp message in campaign '{campaign.Name}': {imageEx.Message}. Sent message without image.");
                    }
                }
                else
                {
                    // Send regular WhatsApp template message when no image is present
                    await _whatsAppMessageService.SendTemplateMessageAsync(
                        mobileNumber, 
                        campaign.WhatsAppTemplateName, 
                        parameters
                    );
                    
                    await _logger.InformationAsync($"{nameof(MassEmailCampaignSendTask)} - WhatsApp message sent successfully to {mobileNumber.MaskMobileNumber()} using template '{campaign.WhatsAppTemplateName}' for campaign '{campaign.Name}'");
                }
            }
            catch (Exception ex)
            {
                await _logger.WarningAsync($"{nameof(MassEmailCampaignSendTask)} - Failed to send WhatsApp message to profile {profile.Id} for campaign '{campaign.Name}' using template '{campaign.WhatsAppTemplateName}': {ex.Message}");
                // Don't throw - WhatsApp failure shouldn't stop email campaign
            }
        }

        #endregion
    }
}